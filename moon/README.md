# Moon - 前端Web应用详细文档

## 📋 项目信息

### 项目概述
Moon是一个基于Next.js的现代化前端Web应用，作为Mega项目的用户界面层，提供代码托管、协作编辑、实时通信等功能。项目采用Monorepo架构，使用pnpm作为包管理器，Turbo作为构建工具。

### 基本信息
- **项目名称**: @gitmono/monorepo
- **版本**: 0.0.0
- **包管理器**: pnpm@9.7.1
- **Node版本要求**: >=20.0.0 <21
- **构建工具**: Turbo + Next.js
- **开发语言**: TypeScript + React

## 🔄 项目核心流程

### 1. 用户认证流程
```
用户访问 → 检查登录状态 → 未登录重定向到登录页 → 登录成功后重定向到组织首页
```

### 2. 组织管理流程
```
用户登录 → 选择/创建组织 → 进入组织工作空间 → 访问项目/聊天/代码等功能
```

### 3. 实时协作流程
```
用户编辑内容 → Y.js同步状态 → Hocuspocus服务器 → 实时广播给其他用户
```

### 4. 代码审查流程
```
创建MR → 代码差异展示 → 评论讨论 → 检查状态 → 合并/关闭
```

## 📁 目录结构详解

### 根目录结构
```
moon/
├── api/                    # API相关文件
├── apps/                   # 应用程序
│   ├── sync-server/       # 实时同步服务器
│   └── web/               # 主Web应用
├── packages/              # 共享包
├── patches/               # 第三方库补丁
├── script/                # 构建脚本
├── turbo/                 # Turbo缓存
├── package.json           # 根包配置
├── pnpm-workspace.yaml    # pnpm工作空间配置
└── turbo.json            # Turbo构建配置
```

### apps/web - 主Web应用
```
apps/web/
├── app/                   # Next.js App Router (新)
│   └── robots.ts         # SEO机器人配置
├── pages/                 # Next.js Pages Router (主要)
│   ├── [org]/            # 组织相关页面
│   │   ├── home/         # 首页
│   │   ├── chat/         # 聊天功能
│   │   ├── posts/        # 帖子管理
│   │   ├── people/       # 人员管理
│   │   ├── calls/        # 视频通话
│   │   ├── notes/        # 笔记功能
│   │   └── mr/           # Merge Request
│   ├── api/              # API路由
│   ├── auth/             # 认证页面
│   ├── new/              # 新建组织
│   ├── _app.tsx          # 应用入口
│   └── _document.tsx     # HTML文档结构
├── components/            # React组件
│   ├── Layout/           # 布局组件
│   ├── Providers/        # Context提供者
│   ├── NavigationBar/    # 导航栏
│   ├── Sidebar/          # 侧边栏
│   ├── CommandMenu/      # 命令菜单
│   ├── MarkdownEditor/   # Markdown编辑器
│   ├── Call/             # 视频通话
│   ├── Chat/             # 聊天功能
│   ├── Post/             # 帖子组件
│   ├── Comments/         # 评论系统
│   ├── AttachmentGrid/   # 附件网格
│   └── ...              # 其他功能组件
├── hooks/                # 自定义Hooks
├── utils/                # 工具函数
├── contexts/             # React Context
├── atoms/                # Jotai状态原子
├── styles/               # 样式文件
├── public/               # 静态资源
├── playwright/           # E2E测试
└── 配置文件...
```

### packages - 共享包
```
packages/
├── config/               # 配置包
│   └── src/index.ts     # API端点配置
├── types/                # TypeScript类型定义
├── ui/                   # UI组件库
│   └── src/             # 组件源码
├── editor/               # 编辑器包
├── demo-content/         # 演示内容
├── regex/                # 正则表达式工具
├── tsconfig/             # TypeScript配置
└── eslint-config-gitmono/ # ESLint配置
```

## 🎨 代码风格与规范

### TypeScript配置
- 严格模式启用
- 路径别名: `@/` 指向 `apps/web/`
- 共享配置: `@gitmono/tsconfig`

### ESLint规范
- 基础配置: `@gitmono/eslint-config`
- Next.js专用规则
- React内部规则
- Storybook规则

### 样式规范
- **CSS框架**: Tailwind CSS
- **组件样式**: CSS-in-JS (Emotion)
- **动画**: Framer Motion
- **图标**: Heroicons + Primer Octicons
- **主题**: 支持深色/浅色模式

### 代码组织规范
- 组件采用函数式组件 + Hooks
- 状态管理使用Jotai原子化状态
- 文件命名采用PascalCase (组件) 和 camelCase (工具)
- 导入顺序: 第三方库 → 内部包 → 相对路径

## 🛠️ 核心技术栈

### 前端框架
- **Next.js 14**: React全栈框架，支持SSR/SSG
- **React 18**: 用户界面库，支持并发特性
- **TypeScript**: 静态类型检查

### 状态管理
- **Jotai**: 原子化状态管理，替代Redux
- **TanStack Query**: 服务端状态管理和缓存
- **Zustand**: 轻量级状态管理（部分场景）

### UI组件库
- **Radix UI**: 无样式的可访问组件基础
- **Tailwind CSS**: 原子化CSS框架
- **Headless UI**: 无样式组件库
- **Framer Motion**: 动画库
- **React Hook Form**: 表单管理

### 实时通信
- **Hocuspocus**: Y.js协作编辑服务器
- **Y.js**: 实时协作数据结构
- **Pusher**: WebSocket实时通信
- **Server-Sent Events**: 服务器推送

### 编辑器相关
- **TipTap**: 富文本编辑器框架
- **ProseMirror**: 编辑器核心引擎
- **Monaco Editor**: 代码编辑器
- **Prism**: 语法高亮

### 媒体处理
- **@100mslive/react-sdk**: 视频通话SDK
- **React Lottie Player**: Lottie动画播放
- **React Image File Resizer**: 图片压缩

### 开发工具
- **Storybook**: 组件开发环境
- **Playwright**: E2E测试框架
- **Vitest**: 单元测试框架
- **ESLint + Prettier**: 代码格式化

### 构建部署
- **Turbo**: Monorepo构建工具
- **pnpm**: 包管理器
- **Docker**: 容器化部署
- **Vercel**: 部署平台

### 监控分析
- **Sentry**: 错误监控
- **Vercel Analytics**: 性能分析
- **Axiom**: 日志分析

## 🔧 开发命令

```bash
# 安装依赖
pnpm install

# 开发模式
pnpm dev

# 构建项目
pnpm build

# 代码检查
pnpm lint

# 代码格式化
pnpm format

# 运行测试
pnpm test

# E2E测试
pnpm playwright

# Storybook
pnpm storybook
```

## 🌐 环境配置

### API端点配置
- **主API**: api.gitmega.com
- **Git API**: git.gitmega.com  
- **Orion API**: orion.gitmega.com
- **同步服务**: sync服务器地址

### 环境变量
- `NEXT_PUBLIC_*`: 客户端环境变量
- `SENTRY_*`: 错误监控配置
- `TIPTAP_*`: 编辑器许可证配置

## 📂 详细文件说明

### 核心配置文件
- **package.json**: 项目依赖和脚本配置
- **turbo.json**: Turbo构建任务配置
- **pnpm-workspace.yaml**: pnpm工作空间配置，定义了catalog依赖管理
- **next.config.js**: Next.js配置，包含transpile包、图片域名等
- **tailwind.config.js**: Tailwind CSS配置
- **tsconfig.json**: TypeScript编译配置

### 关键组件文件详解

#### Layout组件 (components/Layout/)
- **AppLayout.tsx**: 应用主布局，包含导航栏、侧边栏、主内容区
- **TabLayout.tsx**: 标签页布局，用于MR详情页的会话/检查/文件变更切换

#### 导航组件 (components/NavigationBar/)
- **index.tsx**: 主导航栏组件，响应式设计，移动端/桌面端不同展示
- **MobileTabBar**: 移动端底部标签栏
- **RefetchingPageIndicator**: 页面刷新指示器

#### 侧边栏组件 (components/Sidebar/)
- **index.tsx**: 主侧边栏容器
- **SidebarHome**: 首页链接
- **SidebarInbox**: 收件箱
- **SidebarCode**: 代码相关导航
- **SidebarMergeRequest**: MR导航
- **SidebarProjectsGroup**: 项目分组

#### 编辑器组件 (components/MarkdownEditor/)
- **index.tsx**: Markdown编辑器主组件，基于TipTap
- **EditorBubbleMenu**: 编辑器气泡菜单
- **SlashCommand**: 斜杠命令功能
- **MentionList**: @提及功能

#### 实时协作 (components/Post/Notes/)
- **useEditorSync.ts**: 编辑器同步Hook，使用Y.js和Hocuspocus

### 页面文件详解

#### 动态路由页面 (pages/[org]/)
- **home/index.tsx**: 组织首页，显示动态和收藏
- **chat/[threadId]/index.tsx**: 聊天线程详情页
- **posts/[postId]/index.tsx**: 帖子详情页
- **people/index.tsx**: 团队成员页面
- **calls/join/[callRoomId]/index.tsx**: 视频通话加入页面
- **mr/[link]/index.tsx**: Merge Request详情页，包含会话、检查、文件变更

#### 特殊页面
- **_app.tsx**: Next.js应用入口，配置全局样式和Provider
- **_document.tsx**: HTML文档结构
- **index.tsx**: 根页面，处理组织重定向逻辑
- **new/index.tsx**: 新建组织页面

### Hooks目录 (hooks/)
包含400+个自定义Hook，按功能分类：
- **useCreate***: 创建相关操作
- **useGet***: 数据获取操作
- **useUpdate***: 更新操作
- **useDelete***: 删除操作
- **SSE/**: Server-Sent Events相关
- **conversation/**: 会话相关
- **reactions/**: 反应表情相关

### 工具函数 (utils/)
- **queryClient.ts**: TanStack Query客户端配置
- **apiErrorToast.ts**: API错误提示
- **atomWith*.ts**: Jotai原子工具函数
- **normy/**: 数据规范化工具
- **reactions/**: 表情反应工具

### 样式文件 (styles/)
- **global.css**: 全局样式
- **editor.css**: 编辑器专用样式
- **prose.css**: 文章内容样式

### 静态资源 (public/)
- **icons/**: 应用图标
- **images/**: 图片资源
- **sounds/**: 音效文件
- **rust/**: Rust相关资源
- **service_worker.js**: PWA服务工作者

## 🔄 数据流架构

### 状态管理层次
1. **全局状态**: Jotai atoms (atoms/)
2. **服务端状态**: TanStack Query
3. **组件状态**: React useState/useReducer
4. **表单状态**: React Hook Form
5. **实时状态**: Y.js + Hocuspocus

### API通信
- **主API**: Rails后端API
- **Legacy API**: Mono服务API
- **Orion API**: 构建服务API
- **实时同步**: WebSocket + SSE

## 🎯 核心功能模块

### 1. 代码托管功能
- **文件浏览**: 代码树形结构展示
- **差异对比**: @git-diff-view/react
- **MR工作流**: 创建、审查、合并
- **代码高亮**: Prism语法高亮

### 2. 实时协作编辑
- **协作引擎**: Y.js CRDT算法
- **同步服务**: Hocuspocus服务器
- **冲突解决**: 自动合并编辑冲突
- **离线支持**: 本地缓存和同步

### 3. 团队沟通
- **即时聊天**: 实时消息传递
- **视频通话**: 100ms SDK集成
- **屏幕共享**: WebRTC技术
- **文件共享**: 拖拽上传和预览

### 4. 项目管理
- **任务跟踪**: 项目和任务管理
- **里程碑**: 项目进度跟踪
- **权限控制**: 细粒度访问控制
- **通知系统**: 实时通知和邮件

### 5. Rust生态工具
- **Crate分析**: 依赖关系可视化
- **CVE检测**: 安全漏洞扫描
- **文档集成**: Rust文档聚合
- **社区动态**: Rust新闻和更新

这个项目展现了现代前端开发的最佳实践，结合了实时协作、代码托管、团队沟通等多种功能，是一个功能完整的企业级协作平台前端应用。
