{"name": "@gitmono/sync-server", "version": "0.0.0", "private": true, "scripts": {"build": "tsup", "clean": "rm -rf .turbo dist node_modules", "dev": "tsup --watch --onSuccess \"node dist/index.mjs\"", "sentry:sourcemaps": "sentry-cli sourcemaps inject --org mega --project sync-server ./dist && sentry-cli sourcemaps upload --org mega --project sync-server --log-level=debug ./dist", "start": "node dist/index.mjs"}, "dependencies": {"@gitmono/editor": "workspace:*", "@gitmono/types": "workspace:*", "@hocuspocus/extension-database": "catalog:", "@hocuspocus/extension-logger": "catalog:", "@hocuspocus/server": "catalog:", "@hocuspocus/transformer": "catalog:", "@sentry/node": "catalog:", "@tiptap/core": "catalog:", "@tiptap/html": "catalog:", "@tiptap/pm": "catalog:", "dotenv": "catalog:", "js-base64": "catalog:", "yjs": "catalog:"}, "devDependencies": {"@gitmono/tsconfig": "workspace:*", "@flydotio/dockerfile": "catalog:", "@sentry/cli": "catalog:", "tsup": "catalog:", "typescript": "catalog:"}}