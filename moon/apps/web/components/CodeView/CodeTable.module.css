.fileTableIcon {
  width: 23px;
  height: 23px;
  margin-right: 8px;
}

.readmeContainer {
  margin-top: 2rem;
  margin-left: 1rem;
}

.markdownContent {
  margin: 0 auto;
  margin-top: 5%;
  border: 1px solid rgba(0, 0, 0, 0.112);
  padding: 2%;
  border-radius: 0.5rem;
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);

}
