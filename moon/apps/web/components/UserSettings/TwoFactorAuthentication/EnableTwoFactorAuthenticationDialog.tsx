import { useEffect, useState } from 'react'
import { toast } from 'react-hot-toast'
import QRCode from 'react-qr-code'

import { <PERSON><PERSON>, CheckIcon, ClipboardIcon, FormError, MutationError, TextField, UIText } from '@gitmono/ui'
import * as Dialog from '@gitmono/ui/src/Dialog'
import { useCopyToClipboard } from '@gitmono/ui/src/hooks'

import { useUpdateTwoFactorAuthentication } from '@/hooks/useUpdateTwoFactorAuthentication'

interface Props {
  open: boolean
  onOpenChange: (bool: boolean) => void
  onComplete: (bool: boolean) => void
  provisioningUri: string | null
}

export function EnableTwoFactorAuthenticationDialog({ open, onOpenChange, onComplete, provisioningUri }: Props) {
  const [copy, isCopied] = useCopyToClipboard()
  const updateTwoFactorAuthentication = useUpdateTwoFactorAuthentication()
  const [currentPassword, setCurrentPassword] = useState('')
  const [authenticationCode, setAuthenticationCode] = useState('')
  const [backupCodes, setBackupCodes] = useState([])
  const twoFactorEnabled = backupCodes.length !== 0

  async function handleSubmit(e: any) {
    e.preventDefault()

    updateTwoFactorAuthentication.mutate(
      {
        otp_attempt: authenticationCode,
        password: currentPassword
      },
      {
        onSuccess: async (data: any) => {
          toast('Successfully enabled two-factor authentication.')
          setBackupCodes(data.two_factor_backup_codes)
        }
      }
    )
  }

  useEffect(() => {
    setAuthenticationCode('')
    setCurrentPassword('')
  }, [open])

  return (
    <Dialog.Root open={open} onOpenChange={onOpenChange}>
      <Dialog.Header>
        <Dialog.Title>{twoFactorEnabled ? 'Backup codes' : 'Enable two-factor authentication'}</Dialog.Title>
        {twoFactorEnabled && (
          <Dialog.Description>
            Keep your recovery codes as safe as your password. We recommend saving them with a password manager such as
            Lastpass, 1Password, or Keeper.
          </Dialog.Description>
        )}
      </Dialog.Header>

      <Dialog.Content>
        {!twoFactorEnabled && (
          <>
            <UIText secondary>
              Scan the QR code with your authenticator app. If you don’t use a 2FA authenticator app, we recommend
              Google Authenticator for{' '}
              <a
                target='_blank'
                rel='noopener noreferrer'
                className='text-blue-500'
                href='https://apps.apple.com/us/app/google-authenticator/id388497605'
              >
                iOS
              </a>{' '}
              and{' '}
              <a
                target='_blank'
                rel='noopener noreferrer'
                className='text-blue-500'
                href='https://play.google.com/store/apps/details?id=com.google.android.apps.authenticator2&hl=en_US&gl=US'
              >
                Android
              </a>
              , or{' '}
              <a target='_blank' rel='noopener noreferrer' className='text-blue-500' href='https://1password.com/'>
                1Password
              </a>
              .
            </UIText>
            <div className='mt-4 flex items-center justify-center rounded-lg border bg-black bg-opacity-5 p-8 dark:bg-neutral-50'>
              {provisioningUri && <QRCode size={144} value={provisioningUri} />}
            </div>

            <form onSubmit={handleSubmit}>
              <div className='mt-6 flex flex-col gap-4'>
                <div className='flex flex-col gap-2'>
                  <UIText secondary>
                    After scanning the QR code above, enter the six-digit code generated by your authenticator app.
                  </UIText>
                  <TextField
                    id='authentication-code'
                    name='authentication-code'
                    label='Authentication code'
                    labelHidden
                    placeholder='Authentication code'
                    onChange={(value) => setAuthenticationCode(value)}
                    value={authenticationCode}
                    required
                  />
                </div>
                <div className='flex flex-col gap-2'>
                  <UIText secondary>Enter your Campsite account password to confirm your identity.</UIText>
                  <TextField
                    type='password'
                    id='current-password'
                    name='current-password'
                    label='Current password'
                    labelHidden
                    placeholder='Current password'
                    onChange={(value) => setCurrentPassword(value)}
                    value={currentPassword}
                    required
                  />
                </div>

                <FormError>
                  <MutationError mutation={updateTwoFactorAuthentication} />
                </FormError>
              </div>
            </form>
          </>
        )}

        {twoFactorEnabled && (
          <div>
            <div className='grid select-text grid-cols-2 gap-3 rounded-lg border border-black border-opacity-5 bg-neutral-50 p-3'>
              {backupCodes.map((code, index) => (
                // eslint-disable-next-line react/no-array-index-key
                <UIText secondary className='font-mono' key={index}>
                  {code}
                </UIText>
              ))}
              <div className='col-span-2'>
                <Button
                  onClick={() => copy(`${backupCodes.join('\r\n')}`)}
                  variant='important'
                  leftSlot={isCopied ? <CheckIcon /> : <ClipboardIcon />}
                  fullWidth
                >
                  {isCopied ? 'Copied' : 'Copy codes'}
                </Button>
              </div>
            </div>
          </div>
        )}
      </Dialog.Content>

      {!twoFactorEnabled && (
        <Dialog.Footer>
          <Dialog.TrailingActions>
            <Button disabled={updateTwoFactorAuthentication.isPending} onClick={() => onOpenChange(false)}>
              Cancel
            </Button>

            <Button
              disabled={updateTwoFactorAuthentication.isPending}
              loading={updateTwoFactorAuthentication.isPending}
              type='submit'
              variant='primary'
              onClick={handleSubmit}
            >
              Enable
            </Button>
          </Dialog.TrailingActions>
        </Dialog.Footer>
      )}

      {twoFactorEnabled && (
        <Dialog.Footer>
          <Dialog.TrailingActions>
            <Button variant='primary' onClick={() => onComplete(true)}>
              Done
            </Button>
          </Dialog.TrailingActions>
        </Dialog.Footer>
      )}
    </Dialog.Root>
  )
}
