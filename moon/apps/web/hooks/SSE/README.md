# SSE Hooks 详细文档

## 📋 文件夹概述

### 1. 文件夹详细介绍

`hooks/SSE/` 文件夹包含了与**Server-Sent Events (SSE)** 和**构建任务管理**相关的React Hooks。这些hooks主要用于：

- **构建任务查询**: 获取MR相关的构建任务信息
- **任务状态监控**: 实时监控构建任务的执行状态  
- **日志获取**: 获取构建任务的输出日志
- **构建列表管理**: 管理任务的构建列表

### 2. 文件结构
```
hooks/SSE/
├── ssmRequest.ts           # 核心API请求函数集合
├── useGetBuildList.ts      # 获取构建列表Hook
├── useGetHTTPLog.ts        # 获取HTTP日志Hook
├── useGetMrTask.ts         # 获取MR任务Hook
├── useGetMrTaskStatus.ts   # 获取MR任务状态Hook
└── useGetTaskStatus.ts     # 获取单个任务状态Hook
```

## 🔗 与 packages/types/generated.ts 的关系

### 类型依赖关系

SSE hooks与`packages/types/generated.ts`有着密切的类型依赖关系：

#### 1. 核心类型定义
```typescript
// 来自 packages/types/generated.ts
export type GetTasksByMrData = TaskInfoDTO[]
export type GetTaskBuildListByIdData = string[]
export type GetTaskHistoryOutputByIdData = any
export type GetTaskHistoryOutputByIdParams = {
  type: string
  offset?: number
  limit?: number
  id: string
}
```

#### 2. 任务相关类型
```typescript
// TaskInfoDTO - 任务信息数据传输对象
export type TaskInfoDTO = {
  build_list: BuildDTO[]
  created_at: string
  mr_id: number
  task_id: string
  task_name?: string | null
  template?: any
}

// BuildDTO - 构建信息数据传输对象
export type BuildDTO = {
  args?: any[] | null
  created_at: string
  end_at?: string | null
  exit_code?: number | null
  id: string
  output_file: string
  repo: string
  start_at: string
  status: TaskStatusEnum
  target: string
  task_id: string
}

// TaskStatusEnum - 任务状态枚举
export enum TaskStatusEnum {
  Pending = 'Pending',
  Building = 'Building', 
  Interrupted = 'Interrupted',
  Failed = 'Failed',
  Completed = 'Completed',
  NotFound = 'NotFound'
}
```

#### 3. 类型使用映射
| SSE Hook | 使用的Generated类型 | 作用 |
|----------|-------------------|------|
| `useGetMrTask` | `GetTasksByMrData` | MR任务列表数据 |
| `useGetBuildList` | `GetTaskBuildListByIdData` | 构建列表数据 |
| `useGetHTTPLog` | `GetTaskHistoryOutputByIdData`, `GetTaskHistoryOutputByIdParams` | 日志数据和参数 |
| `useGetMrTaskStatus` | 自定义`MRTaskStatus[]` | MR任务状态 |
| `useGetTaskStatus` | 自定义`TaskStatus` | 单个任务状态 |

## 🛠️ 开发流程指南

### 参考 ssmRequest.ts 的开发模式

#### 1. ssmRequest.ts 代码结构分析

```typescript
// 1. 导入类型定义
import {
  GetTaskBuildListByIdData,
  GetTaskHistoryOutputByIdData,
  GetTaskHistoryOutputByIdParams,
  GetTasksByMrData
} from '@gitmono/types/generated'

// 2. 导入配置
import { SSEPATH } from '@/components/MrView/hook/useSSM'

// 3. 定义API请求函数
export const fetchAllbuildList = async (id: string): Promise<GetTaskBuildListByIdData> => {
  const res = await fetch(`${SSEPATH}task-build-list/${id}`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json'
    }
  })

  if (!res.ok) {
    throw new Error(`HTTP error ${res.status}`)
  }
  return res.json()
}
```

#### 2. 开发 useGetBuildList.ts 的完整流程

##### 步骤1: 分析需求
- **功能**: 获取指定任务ID的构建列表
- **参数**: `id: string` (任务ID)
- **返回**: `GetTaskBuildListByIdData` (构建列表数据)

##### 步骤2: 修复现有问题
当前代码存在问题：
```typescript
// ❌ 错误：函数名与文件名不匹配
export function useGetHTTPLog(id: string) {
  return useQuery<GetTaskBuildListByIdData, Error>({
    queryKey: [id],
    queryFn: () => fetchAllbuildList(id)
    // 注释掉的配置选项
  })
}
```

##### 步骤3: 正确的实现
```typescript
import { useQuery } from '@tanstack/react-query'
import { GetTaskBuildListByIdData } from '@gitmono/types/generated'
import { fetchAllbuildList } from './ssmRequest'

export function useGetBuildList(id: string) {
  return useQuery<GetTaskBuildListByIdData, Error>({
    queryKey: ['buildList', id], // 更具体的queryKey
    queryFn: () => fetchAllbuildList(id),
    refetchInterval: 15000, // 每15秒刷新
    refetchIntervalInBackground: true, // 后台刷新
    enabled: !!id // 只有当id存在时才启用查询
  })
}
```

##### 步骤4: 配置选项说明
```typescript
// 完整配置版本
export function useGetBuildList(id: string, options?: {
  enabled?: boolean
  refetchInterval?: number
  refetchOnWindowFocus?: boolean
}) {
  return useQuery<GetTaskBuildListByIdData, Error>({
    queryKey: ['buildList', id],
    queryFn: () => fetchAllbuildList(id),
    
    // 轮询配置
    refetchInterval: options?.refetchInterval ?? 15000,
    refetchIntervalInBackground: true,
    
    // 启用条件
    enabled: (options?.enabled ?? true) && !!id,
    
    // 窗口焦点刷新
    refetchOnWindowFocus: options?.refetchOnWindowFocus ?? false,
    
    // 缓存配置
    staleTime: 5000, // 5秒内认为数据是新鲜的
    gcTime: 10 * 60 * 1000, // 10分钟后清理缓存
    
    // 错误重试
    retry: 3,
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000)
  })
}
```

### 3. 标准开发流程

#### 第一步: 确定API端点和数据类型
1. 查看后端API文档，确定端点路径
2. 在`packages/types/generated.ts`中找到对应的类型定义
3. 确定请求参数和响应数据的类型

#### 第二步: 在ssmRequest.ts中添加API函数
```typescript
export const yourApiFunction = async (params: YourParamsType): Promise<YourResponseType> => {
  const res = await fetch(`${SSEPATH}your-endpoint/${params.id}`, {
    method: 'GET', // 或 POST, PUT, DELETE
    headers: {
      'Content-Type': 'application/json'
    },
    // 如果是POST请求，添加body
    // body: JSON.stringify(params)
  })

  if (!res.ok) {
    throw new Error(`HTTP error ${res.status}`)
  }
  return res.json()
}
```

#### 第三步: 创建对应的Hook
```typescript
import { useQuery } from '@tanstack/react-query'
import { YourResponseType, YourParamsType } from '@gitmono/types/generated'
import { yourApiFunction } from './ssmRequest'

export function useYourHook(params: YourParamsType) {
  return useQuery<YourResponseType, Error>({
    queryKey: ['yourHook', params], // 唯一标识
    queryFn: () => yourApiFunction(params),
    
    // 根据业务需求配置
    enabled: !!params.id, // 启用条件
    refetchInterval: 15000, // 轮询间隔
    refetchIntervalInBackground: true, // 后台轮询
    staleTime: 5000, // 缓存时间
    retry: 3 // 重试次数
  })
}
```

#### 第四步: 测试和优化
1. 在组件中使用Hook
2. 测试各种场景（成功、失败、加载中）
3. 根据实际需求调整配置参数

### 4. 最佳实践

#### QueryKey命名规范
```typescript
// ✅ 好的命名
queryKey: ['buildList', id]
queryKey: ['mrTask', mrId]
queryKey: ['taskStatus', taskId]

// ❌ 避免的命名
queryKey: [id] // 太简单，可能冲突
queryKey: ['data', id] // 太通用
```

#### 错误处理
```typescript
export function useGetBuildList(id: string) {
  return useQuery<GetTaskBuildListByIdData, Error>({
    queryKey: ['buildList', id],
    queryFn: () => fetchAllbuildList(id),
    enabled: !!id,
    
    // 自定义错误处理
    throwOnError: false, // 不抛出错误，通过error状态处理
    retry: (failureCount, error) => {
      // 根据错误类型决定是否重试
      if (error.message.includes('404')) {
        return false // 404错误不重试
      }
      return failureCount < 3
    }
  })
}
```

#### 条件查询
```typescript
export function useGetBuildList(id: string, enabled = true) {
  return useQuery<GetTaskBuildListByIdData, Error>({
    queryKey: ['buildList', id],
    queryFn: () => fetchAllbuildList(id),
    enabled: enabled && !!id && id.length > 0 // 多重条件检查
  })
}
```

这个开发流程确保了代码的一致性、类型安全性和可维护性，同时充分利用了TanStack Query的强大功能。
