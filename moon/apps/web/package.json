{"name": "@gitmono/web", "version": "0.0.0", "private": true, "scripts": {"analyze": "ANALYZE=true next build", "build": "next build", "build-storybook": "storybook build", "clean": "rm -rf .next .turbo .vercel node_modules", "dev": "next dev -p 80", "lint": "next lint", "playwright": "playwright test", "start": "next start -p 3000 -H 0.0.0.0", "storybook": "storybook dev -p 6006", "test": "vitest"}, "dependencies": {"@100mslive/hms-noise-cancellation": "catalog:", "@100mslive/hms-video-store": "catalog:", "@100mslive/react-sdk": "catalog:", "@emoji-mart/data": "catalog:", "@emotion/styled": "catalog:", "@git-diff-view/react": "catalog:", "@gitmono/config": "workspace:*", "@gitmono/editor": "workspace:*", "@gitmono/regex": "workspace:*", "@gitmono/ui": "workspace:*", "@heroicons/react": "catalog:", "@hocuspocus/provider": "catalog:", "@hookform/resolvers": "catalog:", "@melloware/react-logviewer": "^6.3.2", "@mui/icons-material": "catalog:", "@mui/material": "catalog:", "@mui/x-tree-view": "catalog:", "@next/bundle-analyzer": "catalog:", "@octokit/auth-app": "catalog:", "@octokit/core": "catalog:", "@primer/octicons-react": "catalog:", "@primer/primitives": "catalog:", "@primer/react": "catalog:", "@radix-ui/react-accordion": "catalog:", "@radix-ui/react-collapsible": "catalog:", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "catalog:", "@radix-ui/react-hover-card": "catalog:", "@radix-ui/react-popover": "catalog:", "@radix-ui/react-portal": "catalog:", "@radix-ui/react-primitive": "^2.1.3", "@radix-ui/react-radio-group": "catalog:", "@radix-ui/react-slider": "catalog:", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "catalog:", "@radix-ui/themes": "catalog:", "@sentry/nextjs": "catalog:", "@shopify/react-shortcuts": "catalog:", "@swc/helpers": "^0.5.17", "@tanstack/react-query": "catalog:", "@tanstack/react-virtual": "catalog:", "@tiptap/extension-collaboration": "catalog:", "@tiptap/extension-collaboration-cursor": "catalog:", "@tiptap/pm": "catalog:", "@tiptap/react": "catalog:", "@todesktop/client-core": "catalog:", "@types/d3": "catalog:", "@vercel/og": "catalog:", "@vercel/speed-insights": "catalog:", "clsx": "catalog:", "colord": "catalog:", "cookies-next": "catalog:", "copy-to-clipboard": "catalog:", "d3": "catalog:", "d3-selection": "catalog:", "d3-zoom": "catalog:", "date-fns": "catalog:", "deepmerge": "catalog:", "fast-deep-equal": "catalog:", "framer-motion": "catalog:", "fuse.js": "catalog:", "github-markdown-css": "catalog:", "gray-matter": "catalog:", "jotai": "catalog:", "jotai-scope": "catalog:", "js-base64": "catalog:", "jszip": "catalog:", "linkify-react": "catalog:", "linkifyjs": "catalog:", "lottie-web": "catalog:", "material-file-icons": "catalog:", "metascraper": "catalog:", "metascraper-audio": "catalog:", "metascraper-author": "catalog:", "metascraper-date": "catalog:", "metascraper-description": "catalog:", "metascraper-image": "catalog:", "metascraper-lang": "catalog:", "metascraper-logo": "catalog:", "metascraper-logo-favicon": "catalog:", "metascraper-publisher": "catalog:", "metascraper-readability": "catalog:", "metascraper-spotify": "catalog:", "metascraper-title": "catalog:", "metascraper-twitter": "catalog:", "metascraper-url": "catalog:", "metascraper-video": "catalog:", "metascraper-youtube": "catalog:", "next": "catalog:", "next-seo": "catalog:", "next-themes": "catalog:", "nextjs-cors": "catalog:", "node-fetch": "catalog:", "pluralize": "catalog:", "prism-react-renderer": "catalog:", "pusher-js": "catalog:", "react": "catalog:", "react-aria": "catalog:", "react-device-detect": "catalog:", "react-dom": "catalog:", "react-dropzone": "catalog:", "react-hook-form": "catalog:", "react-hot-toast": "catalog:", "react-hotkeys-hook": "catalog:", "react-image-file-resizer": "catalog:", "react-inlinesvg": "catalog:", "react-intersection-observer": "catalog:", "react-lottie-player": "catalog:", "react-markdown": "catalog:", "react-qr-code": "catalog:", "react-refractor": "catalog:", "react-select": "catalog:", "react-timeago": "catalog:", "react-tweet": "catalog:", "react-use-measure": "catalog:", "react-virtuoso": "^4.14.0", "react-wrap-balancer": "catalog:", "remeda": "catalog:", "slugify": "catalog:", "styled-components": "catalog:", "styled-jsx": "^5.1.7", "tippy.js": "catalog:", "use-debounce": "catalog:", "use-sound": "catalog:", "uuid": "catalog:", "vaul": "catalog:", "yjs": "catalog:", "zod": "catalog:"}, "devDependencies": {"@gitmono/eslint-config": "workspace:*", "@gitmono/tsconfig": "workspace:*", "@gitmono/types": "workspace:*", "@playwright/test": "catalog:", "@storybook/addon-docs": "catalog:", "@storybook/addon-essentials": "catalog:", "@storybook/addon-interactions": "catalog:", "@storybook/addon-links": "catalog:", "@storybook/blocks": "catalog:", "@storybook/nextjs": "catalog:", "@storybook/react": "catalog:", "@storybook/test": "catalog:", "@storybook/types": "catalog:", "@swc-jotai/react-refresh": "catalog:", "@tanstack/react-query-devtools": "catalog:", "@testing-library/react": "catalog:", "@tiptap/core": "catalog:", "@types/d3-selection": "catalog:", "@types/d3-transition": "catalog:", "@types/d3-zoom": "catalog:", "@types/node": "catalog:", "@types/pluralize": "catalog:", "@types/react": "catalog:", "@types/react-dom": "catalog:", "@types/react-timeago": "catalog:", "@types/uuid": "catalog:", "@vitejs/plugin-react": "catalog:", "@welldone-software/why-did-you-render": "catalog:", "autoprefixer": "catalog:", "eslint-plugin-storybook": "catalog:", "postcss": "catalog:", "storybook": "catalog:", "tailwindcss": "catalog:", "typescript": "catalog:", "vitest": "catalog:"}, "peerDependencies": {"y-prosemirror": "^1.2.12"}}