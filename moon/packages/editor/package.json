{"name": "@gitmono/editor", "version": "0.0.0", "private": true, "main": "./dist/index.mjs", "module": "./dist/index.mjs", "scripts": {"build": "tsup", "clean": "rm -rf .turbo dist node_modules", "lint": "eslint ./src/*.ts*", "test": "vitest --environment=jsdom"}, "dependencies": {"@gitmono/regex": "workspace:*", "@tiptap-pro/extension-details": "catalog:", "@tiptap-pro/extension-details-content": "catalog:", "@tiptap-pro/extension-details-summary": "catalog:", "@tiptap/core": "catalog:", "@tiptap/extension-blockquote": "catalog:", "@tiptap/extension-bold": "catalog:", "@tiptap/extension-bullet-list": "catalog:", "@tiptap/extension-code": "catalog:", "@tiptap/extension-code-block": "catalog:", "@tiptap/extension-document": "catalog:", "@tiptap/extension-dropcursor": "catalog:", "@tiptap/extension-hard-break": "catalog:", "@tiptap/extension-heading": "catalog:", "@tiptap/extension-history": "catalog:", "@tiptap/extension-horizontal-rule": "catalog:", "@tiptap/extension-italic": "catalog:", "@tiptap/extension-link": "catalog:", "@tiptap/extension-list-item": "catalog:", "@tiptap/extension-list-keymap": "catalog:", "@tiptap/extension-ordered-list": "catalog:", "@tiptap/extension-paragraph": "catalog:", "@tiptap/extension-placeholder": "catalog:", "@tiptap/extension-strike": "catalog:", "@tiptap/extension-task-item": "catalog:", "@tiptap/extension-task-list": "catalog:", "@tiptap/extension-text": "catalog:", "@tiptap/extension-typography": "catalog:", "@tiptap/extension-underline": "catalog:", "@tiptap/pm": "catalog:", "diff": "catalog:", "linkifyjs": "catalog:", "markdown-it": "catalog:", "refractor": "catalog:", "rfc6902": "catalog:", "uuid": "catalog:"}, "devDependencies": {"@gitmono/eslint-config": "workspace:*", "@gitmono/tsconfig": "workspace:*", "@types/diff": "catalog:", "@types/markdown-it": "catalog:", "@types/node": "catalog:", "@types/uuid": "catalog:", "jsdom": "catalog:", "tsup": "catalog:", "typescript": "catalog:", "vitest": "catalog:"}, "peerDependencies": {"prosemirror-model": "^1.22.3"}}