{
  "extends": "@gitmono/tsconfig/base.json",
  "include": ["."],
  "exclude": ["node_modules", "dist"],
  "compilerOptions": {
    "allowJs": true,
    "checkJs": false,
    "outDir": "./dist",
    "sourceMap": true,
    "inlineSources": true,
    // Set `sourceRoot` to  "/" to strip the build path prefix
    // from generated source code references.
    // This improves issue grouping in Sentry.
    "sourceRoot": "/"
  }
}
