{"name": "@gitmono/ui", "version": "0.0.0", "private": true, "main": "./src/index.tsx", "types": "./src/index.tsx", "scripts": {"build:force": "tsc", "clean": "rm -rf .turbo node_modules", "lint": "eslint ./src/**/*.ts*"}, "dependencies": {"@radix-ui/react-context-menu": "catalog:", "@radix-ui/react-dialog": "catalog:", "@radix-ui/react-dropdown-menu": "catalog:", "@radix-ui/react-hover-card": "catalog:", "@radix-ui/react-popover": "catalog:", "@radix-ui/react-popper": "catalog:", "@radix-ui/react-radio-group": "catalog:", "@radix-ui/react-slot": "catalog:", "@radix-ui/react-switch": "catalog:", "@radix-ui/react-toggle-group": "catalog:", "@radix-ui/react-tooltip": "catalog:", "@radix-ui/react-visually-hidden": "catalog:", "@shopify/react-shortcuts": "catalog:", "@todesktop/client-core": "catalog:", "class-variance-authority": "catalog:", "clsx": "catalog:", "date-fns": "catalog:", "framer-motion": "catalog:", "react-day-picker": "catalog:", "react-device-detect": "catalog:", "react-error-boundary": "catalog:", "react-hotkeys-hook": "catalog:", "react-textarea-autosize": "catalog:", "react-timeago": "catalog:", "tailwind-merge": "catalog:", "vaul": "catalog:"}, "devDependencies": {"@gitmono/config": "workspace:*", "@gitmono/eslint-config": "workspace:*", "@gitmono/tsconfig": "workspace:*", "@storybook/react": "catalog:", "@tailwindcss/container-queries": "catalog:", "@tailwindcss/forms": "catalog:", "@tailwindcss/typography": "catalog:", "@types/react": "catalog:", "@types/react-timeago": "catalog:", "postcss": "catalog:", "postcss-import": "catalog:", "tailwind-scrollbar-hide": "catalog:", "tailwindcss": "catalog:", "tailwindcss-animate": "catalog:", "tailwindcss-safe-area": "catalog:", "typescript": "catalog:"}, "peerDependencies": {"@sentry/nextjs": "^8.17.0", "jotai": "^2.0.3", "jotai-scope": "^0.4.1", "next": "^14.2.5", "next-themes": "^0.2.1", "react": "^18.2.0", "react-hot-toast": "^2.4.0"}}