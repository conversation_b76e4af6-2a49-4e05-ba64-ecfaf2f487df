export function Logo({ className }: { className?: string }) {
  return (
    <svg
      width='26'
      height='14'
      viewBox='0 0 34 18'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      className={className}
    >
      <path
        d='M0.140151 16.4649L9.5715 0.490928C9.75112 0.186693 10.0782 0 10.4316 0H25.7959C26.569 0 27.049 0.840284 26.6561 1.50582L17.2247 17.4798C17.0451 17.7841 16.718 17.9708 16.3646 17.9708H1.00028C0.227176 17.9708 -0.252794 17.1305 0.140151 16.4649Z'
        fill='currentColor'
      />
      <path
        d='M22.2255 17.9707H32.9577C33.7108 17.9707 34.193 17.1691 33.8398 16.5042L28.7719 6.96112C28.4064 6.27298 27.4284 6.24978 27.0307 6.91981L21.3666 16.4628C20.9715 17.1284 21.4514 17.9707 22.2255 17.9707Z'
        fill='currentColor'
      />
    </svg>
  )
}

export function LogoSmall() {
  return (
    <svg width='24' height='12' viewBox='0 0 35 18' fill='none' xmlns='http://www.w3.org/2000/svg'>
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M0.989106 16.4917L10.4322 0.491727C10.6121 0.186997 10.9396 0 11.2934 0H26.6769C27.4509 0 27.9315 0.841652 27.5381 1.50827L18.095 17.5083C17.9151 17.813 17.5876 18 17.2338 18H1.8503C1.07624 18 0.595671 17.1583 0.989106 16.4917ZM23.102 17.9999H33.8475C34.6016 17.9999 35.0844 17.1971 34.7308 16.531L29.6565 6.97245C29.2906 6.28319 28.3114 6.25995 27.9132 6.93108L22.242 16.4896C21.8465 17.1562 22.3269 17.9999 23.102 17.9999Z'
        fill='currentColor'
      />
    </svg>
  )
}

export function WordMark() {
  return (
    <svg width='90' height='22' viewBox='0 0 90 22' fill='none' xmlns='http://www.w3.org/2000/svg'>
      <path
        d='M13.225 16.2C12.675 16.5333 12.0083 16.7917 11.225 16.975C10.4417 17.1417 9.61667 17.225 8.75 17.225C7.1 17.225 5.65833 16.8917 4.425 16.225C3.20833 15.5583 2.26667 14.65 1.6 13.5C0.95 12.3333 0.625 11.025 0.625 9.575C0.625 8.09167 0.958333 6.78333 1.625 5.65C2.29167 4.5 3.2 3.59167 4.35 2.925C5.51667 2.25833 6.83333 1.925 8.3 1.925C9.35 1.925 10.275 2.03333 11.075 2.25C11.875 2.45 12.5917 2.76667 13.225 3.2L12.075 6.35C11.6417 5.96667 11.125 5.675 10.525 5.475C9.94167 5.25833 9.29167 5.15 8.575 5.15C7.725 5.15 6.96667 5.325 6.3 5.675C5.63333 6.025 5.10833 6.53333 4.725 7.2C4.35833 7.85 4.175 8.64167 4.175 9.575C4.175 10.4417 4.36667 11.2083 4.75 11.875C5.15 12.5417 5.7 13.0667 6.4 13.45C7.11667 13.8167 7.94167 14 8.875 14C9.75833 14 10.5583 13.8917 11.275 13.675C12.0083 13.4583 12.6083 13.175 13.075 12.825L13.225 16.2ZM24.1893 17H21.0143V16.175C20.6143 16.475 20.1309 16.725 19.5643 16.925C19.0143 17.125 18.4143 17.225 17.7643 17.225C16.5476 17.225 15.6143 16.9333 14.9643 16.35C14.3143 15.7667 13.9893 14.9583 13.9893 13.925C13.9893 12.8083 14.3809 11.9667 15.1643 11.4C15.9643 10.8167 17.0226 10.525 18.3393 10.525C18.8059 10.525 19.2726 10.5667 19.7393 10.65C20.2226 10.7333 20.6143 10.8333 20.9143 10.95V10.425C20.9143 9.80833 20.7143 9.36667 20.3143 9.1C19.9143 8.83333 19.2893 8.7 18.4393 8.7C17.8393 8.7 17.2143 8.775 16.5643 8.925C15.9309 9.075 15.4143 9.25833 15.0143 9.475L14.8893 6.575C15.4226 6.325 16.0393 6.13333 16.7393 6C17.4559 5.85 18.1893 5.775 18.9393 5.775C20.6393 5.775 21.9393 6.13333 22.8393 6.85C23.7393 7.56667 24.1893 8.69167 24.1893 10.225V17ZM20.9143 14.4V12.925C20.6976 12.8083 20.4226 12.725 20.0893 12.675C19.7726 12.6083 19.4643 12.575 19.1643 12.575C18.5643 12.575 18.1059 12.6833 17.7893 12.9C17.4726 13.1 17.3143 13.425 17.3143 13.875C17.3143 14.275 17.4476 14.5667 17.7143 14.75C17.9809 14.9333 18.3809 15.025 18.9143 15.025C19.2643 15.025 19.6309 14.9667 20.0143 14.85C20.3976 14.7333 20.6976 14.5833 20.9143 14.4ZM43.0801 17H39.7301V10.45C39.7301 10.3833 39.7217 10.325 39.7051 10.275C39.7051 10.2083 39.6967 10.1583 39.6801 10.125C39.6301 9.60833 39.4717 9.25 39.2051 9.05C38.9384 8.83333 38.5884 8.725 38.1551 8.725C38.1217 8.725 38.0967 8.73333 38.0801 8.75C38.0634 8.75 38.0384 8.75 38.0051 8.75C37.7217 8.76667 37.4134 8.83333 37.0801 8.95C36.7467 9.05 36.4384 9.19167 36.1551 9.375C36.1717 9.50833 36.1801 9.65 36.1801 9.8C36.1967 9.95 36.2051 10.1 36.2051 10.25V17H32.8551V10.45C32.8551 10.3833 32.8467 10.325 32.8301 10.275C32.8301 10.2083 32.8217 10.1583 32.8051 10.125C32.7551 9.60833 32.5967 9.25 32.3301 9.05C32.0634 8.83333 31.7134 8.725 31.2801 8.725C30.9801 8.725 30.6551 8.78333 30.3051 8.9C29.9551 9.01667 29.6301 9.175 29.3301 9.375V17H25.9801V6.075L29.3301 5.925V6.925C29.6634 6.64167 30.0051 6.425 30.3551 6.275C30.7217 6.10833 31.0801 5.99167 31.4301 5.925C31.8467 5.825 32.2967 5.79167 32.7801 5.825C33.3967 5.85833 33.9551 6 34.4551 6.25C34.9551 6.5 35.3467 6.875 35.6301 7.375C36.1967 6.80833 36.7884 6.4 37.4051 6.15C38.0217 5.9 38.6551 5.775 39.3051 5.775C40.4217 5.775 41.3301 6.1 42.0301 6.75C42.7301 7.38333 43.0801 8.38333 43.0801 9.75V17ZM55.9643 11.375C55.9643 12.4917 55.7226 13.4917 55.2393 14.375C54.7726 15.2583 54.1226 15.9583 53.2893 16.475C52.4726 16.975 51.5309 17.225 50.4643 17.225C49.5309 17.225 48.7893 17.0833 48.2393 16.8V21.625H44.8893V6.075L48.2393 5.925V6.7C48.6059 6.4 49.0143 6.175 49.4643 6.025C49.9309 5.85833 50.4643 5.775 51.0643 5.775C51.9809 5.775 52.8059 6 53.5393 6.45C54.2726 6.9 54.8559 7.54167 55.2893 8.375C55.7393 9.20833 55.9643 10.2083 55.9643 11.375ZM52.5143 11.475C52.5143 10.9083 52.4143 10.4083 52.2143 9.975C52.0309 9.54167 51.7476 9.21667 51.3643 9C50.9976 8.76667 50.5309 8.65 49.9643 8.65C49.2976 8.65 48.7226 8.825 48.2393 9.175V14.075C48.4226 14.1417 48.6559 14.2083 48.9393 14.275C49.2226 14.325 49.5143 14.35 49.8143 14.35C50.3643 14.35 50.8393 14.2333 51.2393 14C51.6393 13.7667 51.9476 13.4333 52.1643 13C52.3976 12.5667 52.5143 12.0583 52.5143 11.475ZM64.784 6.325L64.284 9.05C63.934 8.88333 63.5007 8.74167 62.984 8.625C62.484 8.50833 61.9757 8.45 61.459 8.45C61.0257 8.45 60.7007 8.51667 60.484 8.65C60.284 8.78333 60.184 8.96667 60.184 9.2C60.184 9.38333 60.3007 9.54167 60.534 9.675C60.784 9.80833 61.1007 9.93333 61.484 10.05C61.8673 10.15 62.2757 10.2833 62.709 10.45C63.159 10.6167 63.5757 10.8333 63.959 11.1C64.3423 11.35 64.6507 11.6833 64.884 12.1C65.134 12.5 65.259 13.0083 65.259 13.625C65.259 14.275 65.1007 14.875 64.784 15.425C64.4673 15.9583 63.959 16.3917 63.259 16.725C62.5757 17.0583 61.6757 17.225 60.559 17.225C59.809 17.225 59.0757 17.1417 58.359 16.975C57.6423 16.8083 57.0673 16.5917 56.634 16.325L56.834 13.5C57.2507 13.7667 57.8007 14.0083 58.484 14.225C59.1673 14.4417 59.8507 14.55 60.534 14.55C61.4007 14.55 61.834 14.2917 61.834 13.775C61.834 13.575 61.709 13.4167 61.459 13.3C61.2257 13.1667 60.9173 13.0417 60.534 12.925C60.1507 12.8083 59.734 12.6667 59.284 12.5C58.8507 12.3333 58.4423 12.125 58.059 11.875C57.6757 11.6083 57.359 11.275 57.109 10.875C56.8757 10.4583 56.759 9.94167 56.759 9.325C56.759 8.65833 56.9507 8.05833 57.334 7.525C57.7173 6.99167 58.259 6.56667 58.959 6.25C59.6757 5.93333 60.5173 5.775 61.484 5.775C62.134 5.775 62.7507 5.825 63.334 5.925C63.934 6.00833 64.4173 6.14167 64.784 6.325ZM68.1973 0.899999C68.7139 0.899999 69.1306 1.06667 69.4473 1.4C69.7806 1.73333 69.9473 2.15833 69.9473 2.675C69.9473 3.19167 69.7806 3.61667 69.4473 3.95C69.1306 4.28333 68.7139 4.45 68.1973 4.45C67.6639 4.45 67.2139 4.28333 66.8473 3.95C66.4973 3.61667 66.3223 3.19167 66.3223 2.675C66.3223 2.15833 66.4973 1.73333 66.8473 1.4C67.2139 1.06667 67.6639 0.899999 68.1973 0.899999ZM69.8223 5.925V17H66.4723V6.075L69.8223 5.925ZM78.6119 6L78.3619 8.875H75.5119V12.85C75.5119 13.3833 75.6369 13.7583 75.8869 13.975C76.1536 14.1917 76.5369 14.3 77.0369 14.3C77.3202 14.3 77.5869 14.2583 77.8369 14.175C78.1036 14.075 78.3286 13.9667 78.5119 13.85L78.6369 16.675C78.0369 17.0417 77.2369 17.225 76.2369 17.225C74.8202 17.225 73.7869 16.8833 73.1369 16.2C72.4869 15.5 72.1619 14.5 72.1619 13.2V8.875H70.6869V6H72.1619V3.725L75.5119 3V6H78.6119ZM89.6527 12.325H82.7027C82.8194 12.7417 83.0194 13.1 83.3027 13.4C83.6027 13.7 83.9861 13.925 84.4527 14.075C84.9361 14.225 85.5027 14.3 86.1527 14.3C86.7027 14.3 87.3027 14.225 87.9527 14.075C88.6027 13.925 89.1361 13.725 89.5527 13.475L89.7027 16.325C89.2527 16.6083 88.6611 16.8333 87.9277 17C87.2111 17.15 86.5444 17.225 85.9277 17.225C84.4777 17.225 83.2444 16.9917 82.2277 16.525C81.2277 16.0417 80.4694 15.3667 79.9527 14.5C79.4361 13.6333 79.1777 12.6167 79.1777 11.45C79.1777 10.3 79.4194 9.30833 79.9027 8.475C80.3861 7.625 81.0444 6.96667 81.8777 6.5C82.7277 6.01667 83.6861 5.775 84.7527 5.775C85.7194 5.775 86.5777 5.96667 87.3277 6.35C88.0944 6.73333 88.6944 7.29167 89.1277 8.025C89.5611 8.74167 89.7777 9.61667 89.7777 10.65C89.7777 10.85 89.7611 11.1083 89.7277 11.425C89.7111 11.7417 89.6861 12.0417 89.6527 12.325ZM84.7527 8.175C84.1527 8.175 83.6611 8.38333 83.2777 8.8C82.8944 9.2 82.6611 9.74167 82.5777 10.425H86.6527C86.6527 10.3917 86.6527 10.3417 86.6527 10.275C86.6527 10.1917 86.6527 10.1333 86.6527 10.1C86.6527 9.51667 86.4777 9.05 86.1277 8.7C85.7944 8.35 85.3361 8.175 84.7527 8.175Z'
        fill='currentColor'
      />
    </svg>
  )
}

export function LogoWithWordmark() {
  return (
    <div className='flex scale-[0.88] items-center space-x-2'>
      <Logo />
      <span className='mt-1'>
        <WordMark />
      </span>
    </div>
  )
}
