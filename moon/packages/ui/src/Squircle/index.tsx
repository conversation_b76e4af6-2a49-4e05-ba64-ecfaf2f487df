import { cn } from '../utils'

export function LargeSquircle() {
  return (
    <svg
      width='40'
      height='40'
      viewBox='0 0 40 40'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      className='absolute inset-0 z-0'
    >
      <path
        d='M20 0L19.96581571593477 5.747324437417199L19.863144800385687 8.114051769140591L19.69162528527628 9.909298403838672L19.450627153905703 11.39648137285686L19.139209248608545 12.675889312438311L18.75605236937122 13.797788127625L18.299360406992683 14.791188703041103L17.76671617825189 15.674344805313783L17.15486998695276 16.45942370346837L16.45942370346837 17.15486998695276L15.674344805313783 17.76671617825189L14.791188703041103 18.299360406992683L13.797788127625001 18.75605236937122L12.675889312438311 19.139209248608545L11.396481372856861 19.450627153905703L9.909298403838674 19.69162528527628L8.114051769140593 19.863144800385687L5.747324437417202 19.96581571593477L1.5650219162346277e-7 20L-1.5650219162346277e-7 20L-5.747324437417202 19.96581571593477L-8.114051769140593 19.863144800385687L-9.909298403838674 19.69162528527628L-11.396481372856861 19.450627153905703L-12.675889312438311 19.139209248608545L-13.797788127625001 18.75605236937122L-14.791188703041103 18.299360406992683L-15.674344805313783 17.76671617825189L-16.45942370346837 17.15486998695276L-17.15486998695276 16.45942370346837L-17.76671617825189 15.674344805313783L-18.299360406992683 14.791188703041103L-18.75605236937122 13.797788127625L-19.139209248608545 12.675889312438311L-19.450627153905703 11.39648137285686L-19.69162528527628 9.909298403838672L-19.863144800385687 8.114051769140591L-19.96581571593477 5.747324437417199L-20 0L-20 0L-19.96581571593477 -5.747324437417199L-19.863144800385687 -8.114051769140591L-19.69162528527628 -9.909298403838672L-19.450627153905703 -11.39648137285686L-19.139209248608545 -12.675889312438311L-18.75605236937122 -13.797788127625L-18.299360406992683 -14.791188703041103L-17.76671617825189 -15.674344805313783L-17.15486998695276 -16.45942370346837L-16.45942370346837 -17.15486998695276L-15.674344805313783 -17.76671617825189L-14.791188703041103 -18.299360406992683L-13.797788127625001 -18.75605236937122L-12.675889312438311 -19.139209248608545L-11.396481372856861 -19.450627153905703L-9.909298403838674 -19.69162528527628L-8.114051769140593 -19.863144800385687L-5.747324437417202 -19.96581571593477L-1.5650219162346277e-7 -20L1.5650219162346277e-7 -20L5.747324437417202 -19.96581571593477L8.114051769140593 -19.863144800385687L9.909298403838674 -19.69162528527628L11.396481372856861 -19.450627153905703L12.675889312438311 -19.139209248608545L13.797788127625001 -18.75605236937122L14.791188703041103 -18.299360406992683L15.674344805313783 -17.76671617825189L16.45942370346837 -17.15486998695276L17.15486998695276 -16.45942370346837L17.76671617825189 -15.674344805313783L18.299360406992683 -14.791188703041103L18.75605236937122 -13.797788127625L19.139209248608545 -12.675889312438311L19.450627153905703 -11.39648137285686L19.69162528527628 -9.909298403838672L19.863144800385687 -8.114051769140591L19.96581571593477 -5.747324437417199L20 0Z'
        transform='translate(20,20)'
        fill='currentColor'
      ></path>
    </svg>
  )
}

function SmallSquircle() {
  return (
    <svg
      width='24'
      height='24'
      viewBox='0 0 24 24'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      className='absolute inset-0 z-0'
    >
      <path
        d='M12 0L11.995133002043204 2.407964986405695L11.980528050604708 3.403995578024232L11.956173211781426 4.166207142805854L11.922048390266642 4.806165253454928L11.87812501593103 5.36690785310782L11.82436559437129 5.87038803295049L11.760723110205326 6.3295588159025105L11.687140267849486 6.752791356721459L11.603548549762687 7.1458452078200745L11.509867066429164 7.512868414916555L11.40600116534691 7.856954170799254L11.291840757558628 8.180472894295455L11.167258309225197 8.48528137423857L11.032106431607087 8.772860363781207L10.886214984496977 9.044408412344417L10.729387584112345 9.300907807857733L10.56139737455145 9.543172118626718L10.381981879036323 9.771881230688107L10.190836688786122 9.987607666772975L9.987607666772975 10.190836688786122L9.771881230688109 10.381981879036323L9.543172118626718 10.56139737455145L9.300907807857735 10.729387584112345L9.044408412344417 10.886214984496977L8.772860363781207 11.032106431607087L8.485281374238571 11.167258309225195L8.180472894295457 11.291840757558628L7.856954170799256 11.406001165346908L7.512868414916555 11.509867066429164L7.1458452078200745 11.603548549762687L6.75279135672146 11.687140267849486L6.329558815902511 11.760723110205324L5.8703880329504905 11.82436559437129L5.366907853107821 11.87812501593103L4.806165253454928 11.922048390266642L4.166207142805853 11.956173211781426L3.4039955780242352 11.980528050604708L2.4079649864056982 11.995133002043204L9.390131497407766e-8 12L-9.390131497407766e-8 12L-2.4079649864056982 11.995133002043204L-3.4039955780242352 11.980528050604708L-4.166207142805853 11.956173211781426L-4.806165253454928 11.922048390266642L-5.366907853107821 11.87812501593103L-5.8703880329504905 11.82436559437129L-6.329558815902511 11.760723110205324L-6.75279135672146 11.687140267849486L-7.1458452078200745 11.603548549762687L-7.512868414916555 11.509867066429164L-7.856954170799256 11.406001165346908L-8.180472894295457 11.291840757558628L-8.485281374238571 11.167258309225195L-8.772860363781207 11.032106431607087L-9.044408412344417 10.886214984496977L-9.300907807857735 10.729387584112345L-9.543172118626718 10.56139737455145L-9.771881230688109 10.381981879036323L-9.987607666772975 10.190836688786122L-10.190836688786122 9.987607666772975L-10.381981879036323 9.771881230688107L-10.56139737455145 9.543172118626718L-10.729387584112345 9.300907807857733L-10.886214984496977 9.044408412344417L-11.032106431607087 8.772860363781207L-11.167258309225197 8.48528137423857L-11.291840757558628 8.180472894295455L-11.40600116534691 7.856954170799254L-11.509867066429164 7.512868414916555L-11.603548549762687 7.1458452078200745L-11.687140267849486 6.752791356721459L-11.760723110205326 6.3295588159025105L-11.82436559437129 5.87038803295049L-11.87812501593103 5.36690785310782L-11.922048390266642 4.806165253454928L-11.956173211781426 4.166207142805854L-11.980528050604708 3.403995578024232L-11.995133002043204 2.407964986405695L-12 0L-12 0L-11.995133002043204 -2.407964986405695L-11.980528050604708 -3.403995578024232L-11.956173211781426 -4.166207142805854L-11.922048390266642 -4.806165253454928L-11.87812501593103 -5.36690785310782L-11.82436559437129 -5.87038803295049L-11.760723110205326 -6.3295588159025105L-11.687140267849486 -6.752791356721459L-11.603548549762687 -7.1458452078200745L-11.509867066429164 -7.512868414916555L-11.40600116534691 -7.856954170799254L-11.291840757558628 -8.180472894295455L-11.167258309225197 -8.48528137423857L-11.032106431607087 -8.772860363781207L-10.886214984496977 -9.044408412344417L-10.729387584112345 -9.300907807857733L-10.56139737455145 -9.543172118626718L-10.381981879036323 -9.771881230688107L-10.190836688786122 -9.987607666772975L-9.987607666772975 -10.190836688786122L-9.771881230688109 -10.381981879036323L-9.543172118626718 -10.56139737455145L-9.300907807857735 -10.729387584112345L-9.044408412344417 -10.886214984496977L-8.772860363781207 -11.032106431607087L-8.485281374238571 -11.167258309225195L-8.180472894295457 -11.291840757558628L-7.856954170799256 -11.406001165346908L-7.512868414916555 -11.509867066429164L-7.1458452078200745 -11.603548549762687L-6.75279135672146 -11.687140267849486L-6.329558815902511 -11.760723110205324L-5.8703880329504905 -11.82436559437129L-5.366907853107821 -11.87812501593103L-4.806165253454928 -11.922048390266642L-4.166207142805853 -11.956173211781426L-3.4039955780242352 -11.980528050604708L-2.4079649864056982 -11.995133002043204L-9.390131497407766e-8 -12L9.390131497407766e-8 -12L2.4079649864056982 -11.995133002043204L3.4039955780242352 -11.980528050604708L4.166207142805853 -11.956173211781426L4.806165253454928 -11.922048390266642L5.366907853107821 -11.87812501593103L5.8703880329504905 -11.82436559437129L6.329558815902511 -11.760723110205324L6.75279135672146 -11.687140267849486L7.1458452078200745 -11.603548549762687L7.512868414916555 -11.509867066429164L7.856954170799256 -11.406001165346908L8.180472894295457 -11.291840757558628L8.485281374238571 -11.167258309225195L8.772860363781207 -11.032106431607087L9.044408412344417 -10.886214984496977L9.300907807857735 -10.729387584112345L9.543172118626718 -10.56139737455145L9.771881230688109 -10.381981879036323L9.987607666772975 -10.190836688786122L10.190836688786122 -9.987607666772975L10.381981879036323 -9.771881230688107L10.56139737455145 -9.543172118626718L10.729387584112345 -9.300907807857733L10.886214984496977 -9.044408412344417L11.032106431607087 -8.772860363781207L11.167258309225197 -8.48528137423857L11.291840757558628 -8.180472894295455L11.40600116534691 -7.856954170799254L11.509867066429164 -7.512868414916555L11.603548549762687 -7.1458452078200745L11.687140267849486 -6.752791356721459L11.760723110205326 -6.3295588159025105L11.82436559437129 -5.87038803295049L11.87812501593103 -5.36690785310782L11.922048390266642 -4.806165253454928L11.956173211781426 -4.166207142805854L11.980528050604708 -3.403995578024232L11.995133002043204 -2.407964986405695L12 0Z'
        transform='translate(12,12)'
        fill='currentColor'
      ></path>
    </svg>
  )
}

export function SquircleIconContainer({
  children,
  className,
  size = 'large'
}: {
  children: React.ReactNode
  className?: string
  size?: 'large' | 'small'
}) {
  const Squircle = size === 'small' ? SmallSquircle : LargeSquircle
  const sizeClass = size === 'small' ? 'w-6 h-6' : 'w-10 h-10'

  return (
    <div className={cn('relative flex flex-none items-center justify-center', sizeClass, className)}>
      <Squircle />
      {children}
    </div>
  )
}
