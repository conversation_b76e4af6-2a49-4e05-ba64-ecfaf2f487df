packages:
  - apps/*
  - packages/*
catalog:
  '@100mslive/hms-noise-cancellation': ^0.0.1
  '@100mslive/hms-video-store': ^0.12.21
  '@100mslive/react-sdk': ^0.10.21
  '@babel/plugin-transform-react-jsx': ^7.22.5
  '@create-figma-plugin/build': ^2.6.1
  '@create-figma-plugin/tsconfig': ^2.6.1
  '@create-figma-plugin/ui': ^2.6.1
  '@create-figma-plugin/utilities': ^2.6.1
  '@emoji-mart/data': ^1.1.2
  '@figma/plugin-typings': 1.74.0
  '@flydotio/dockerfile': ^0.4.11
  '@git-diff-view/react': 0.0.26
  '@hocuspocus/extension-database': ^2.13.5
  '@hocuspocus/extension-logger': ^2.13.5
  '@hocuspocus/provider': ^2.13.5
  '@hocuspocus/server': ^2.13.5
  '@hocuspocus/transformer': ^2.13.5
  '@hookform/resolvers': ^3.3.1
  '@ianvs/prettier-plugin-sort-imports': ^4.2.1
  '@mdx-js/loader': ^3.0.1
  '@mdx-js/react': ^3.0.1
  '@next/bundle-analyzer': ^14.2.5
  '@next/mdx': ^14.2.5
  '@octokit/auth-app': ^7.1.0
  '@octokit/core': ^6.1.2
  '@playwright/test': ^1.43.1
  '@primer/octicons-react': ^19.15.3
  '@primer/primitives': ^10.7.0
  '@primer/react': ^37.27.0
  '@radix-ui/react-accordion': ^1.2.0
  '@radix-ui/react-context-menu': ^2.2.1
  '@radix-ui/react-collapsible': ^1.1.1
  '@radix-ui/react-dialog': ^1.1.1
  '@radix-ui/react-dropdown-menu': ^2.1.1
  '@radix-ui/react-hover-card': ^1.1.1
  '@radix-ui/react-popover': ^1.1.1
  '@radix-ui/react-portal': ^1.1.1
  '@radix-ui/react-radio-group': ^1.2.0
  '@radix-ui/react-select': ^2.1.1
  '@radix-ui/react-slider': ^1.2.0
  '@radix-ui/react-popper': ^1.2.0
  '@radix-ui/react-slot': ^1.1.0
  '@radix-ui/react-switch': ^1.1.0
  '@radix-ui/react-tabs': ^1.1.0
  '@radix-ui/react-toggle-group': ^1.1.0
  '@radix-ui/react-tooltip': ^1.1.2
  '@radix-ui/react-visually-hidden': ^1.1.0
  '@radix-ui/themes': '^3.2.1'
  '@sanity/eslint-config-studio': ^4.0.0
  '@sanity/vision': ^3.59.0
  '@sentry/cli': ^2.30.1
  '@sentry/nextjs': ^8.17.0
  '@sentry/node': ^7.106.1
  '@shopify/react-shortcuts': ^5.2.0
  '@storybook/addon-docs': ^8.0.0-beta.3
  '@storybook/addon-essentials': ^8.0.0-beta.3
  '@storybook/addon-interactions': ^8.0.0-beta.3
  '@storybook/addon-links': ^8.0.0-beta.3
  '@storybook/blocks': ^8.0.0-beta.3
  '@storybook/nextjs': ^8.0.0-beta.3
  '@storybook/react': ^8.0.0-beta.3
  '@storybook/test': ^8.0.0-beta.3
  '@storybook/types': ^8.0.0-beta.3
  '@swc-jotai/react-refresh': ^0.0.7
  '@tailwindcss/container-queries': ^0.1.1
  '@tailwindcss/forms': ^0.5.2
  '@tailwindcss/typography': ^0.5.9
  '@tanstack/eslint-plugin-query': ^4.34.1
  '@tanstack/react-query': ^5.56.2
  '@tanstack/react-query-devtools': ^5.56.2
  '@tanstack/react-virtual': ^3.5.0
  '@team-plain/typescript-sdk': ^3.3.0
  '@testing-library/react': ^15.0.7
  '@tiptap-pro/extension-details': ^2.10.11
  '@tiptap-pro/extension-details-content': ^2.10.11
  '@tiptap-pro/extension-details-summary': ^2.10.11
  '@tiptap/core': ^2.6.4
  '@tiptap/extension-blockquote': ^2.6.4
  '@tiptap/extension-bold': ^2.6.4
  '@tiptap/extension-bullet-list': ^2.6.4
  '@tiptap/extension-code': ^2.6.4
  '@tiptap/extension-code-block': ^2.6.4
  '@tiptap/extension-collaboration': ^2.6.4
  '@tiptap/extension-collaboration-cursor': ^2.6.4
  '@tiptap/extension-document': ^2.6.4
  '@tiptap/extension-dropcursor': ^2.6.4
  '@tiptap/extension-hard-break': ^2.6.4
  '@tiptap/extension-heading': ^2.6.4
  '@tiptap/extension-history': ^2.6.4
  '@tiptap/extension-horizontal-rule': ^2.6.4
  '@tiptap/extension-italic': ^2.6.4
  '@tiptap/extension-link': ^2.6.4
  '@tiptap/extension-list-item': ^2.6.4
  '@tiptap/extension-list-keymap': ^2.6.4
  '@tiptap/extension-ordered-list': ^2.6.4
  '@tiptap/extension-paragraph': ^2.6.4
  '@tiptap/extension-placeholder': ^2.6.4
  '@tiptap/extension-strike': ^2.6.4
  '@tiptap/extension-task-item': ^2.6.4
  '@tiptap/extension-task-list': ^2.6.4
  '@tiptap/extension-text': ^2.6.4
  '@tiptap/extension-typography': ^2.6.4
  '@tiptap/extension-underline': ^2.6.4
  '@tiptap/html': ^2.6.4
  '@tiptap/pm': ^2.6.4
  '@tiptap/react': ^2.6.4
  '@todesktop/client-core': ^1.10.0
  '@types/body-parser': ^1.19.5
  '@types/d3': ^7.4.3
  '@types/d3-selection': ^3.0.3
  '@types/d3-transition': ^3.0.3
  '@types/d3-zoom': ^3.0.3
  '@types/diff': ^5.0.5
  '@types/express': ^4.17.21
  '@types/jsdom': ^21.1.6
  '@types/markdown-it': ^13.0.7
  '@types/mdx': ^2.0.13
  '@types/morgan': ^1.9.9
  '@types/node': ^20.12.4
  '@types/pluralize': ^0.0.30
  '@types/react': ^18.2.74
  '@types/react-dom': ^18.2.24
  '@types/react-timeago': ^4.1.3
  '@types/supertest': ^6.0.2
  '@types/uuid': ^9.0.4
  '@typescript-eslint/eslint-plugin': ^7.11.0
  '@typescript-eslint/parser': ^7.11.0
  '@vercel/og': ^0.6.2
  '@vercel/speed-insights': ^1.0.10
  '@vitejs/plugin-react': ^4.3.0
  '@welldone-software/why-did-you-render': ^7.0.1
  autoprefixer: ^10.4.8
  babel-plugin-jsx-pragmatic: ^1.0.2
  body-parser: ^1.20.1
  cheerio: ^1.0.0-rc.12
  class-variance-authority: ^0.7.0
  cmark-gfm-js: ^1.1.7
  concurrently: '>=8'
  cookies-next: ^2.1.1
  d3: ^7.8.5
  d3-selection: ^3.0.0
  d3-zoom: ^3.0.0
  date-fns: ^3.6.0
  deepmerge: ^4.3.1
  diff: ^5.1.0
  dotenv: ^16.3.1
  easymde: '2'
  eslint: ^8.57.0
  eslint-config-next: ^14.2.3
  eslint-config-turbo: ^2.0.9
  eslint-plugin-react: ^7.34.1
  eslint-plugin-react-hooks: ^4.6.2
  eslint-plugin-storybook: ^0.8.0
  eslint-plugin-unused-imports: ^3.2.0
  express: ^4.18.2
  fast-deep-equal: ^3.1.3
  feed: ^4.2.2
  framer-motion: ^11.11.11
  fuse.js: ^7.1.0
  gray-matter: ^4.0.3
  jotai: ^2.0.3
  jotai-scope: ^0.4.1
  js-base64: ^3.7.5
  jsdom: ^24.0.0
  jsx-slack: ^6.1.1
  jszip: ^3.10.1
  linkify-react: ^4.1.3
  linkifyjs: ^4.1.3
  lottie-web: ^5.11.0
  markdown-it: ^14.1.0
  metascraper: ^5.34.8
  metascraper-audio: ^5.37.0
  metascraper-author: ^5.33.7
  metascraper-date: ^5.33.7
  metascraper-description: ^5.34.10
  metascraper-image: ^5.34.1
  metascraper-lang: ^5.34.1
  metascraper-logo: ^5.34.7
  metascraper-logo-favicon: ^5.34.7
  metascraper-publisher: ^5.33.7
  metascraper-readability: ^5.34.1
  metascraper-spotify: ^5.33.8
  metascraper-title: ^5.34.1
  metascraper-twitter: ^5.33.7
  metascraper-url: ^5.33.7
  metascraper-video: ^5.34.7
  metascraper-youtube: ^5.34.7
  morgan: ^1.10.0
  next: ^14.2.5
  next-mdx-remote: ^5.0.0
  next-sanity: ^9.5.0
  next-seo: ^6.0.0
  next-themes: ^0.2.1
  nextjs-cors: ^2.2.0
  node-fetch: ^3.3.0
  onchange: ^7.1.0
  pluralize: ^8.0.0
  postcss: ^8.4.29
  postcss-import: ^15.1.0
  prettier: ^3.2.5
  prettier-plugin-packagejson: ^2.4.14
  prettier-plugin-tailwindcss: ^0.6.8
  pusher-js: ^8.3.0
  react: ^18.2.0
  react-aria: ^3.27.0
  react-confetti: ^6.1.0
  react-day-picker: ^8.10.1
  react-device-detect: ^2.2.2
  react-dom: ^18.2.0
  react-dropzone: ^14.2.2
  react-error-boundary: ^4.0.13
  react-hook-form: ^7.46.1
  react-hot-toast: ^2.4.0
  react-hotkeys-hook: ^4.5.0
  react-image-file-resizer: ^0.4.8
  react-inlinesvg: ^3.0.1
  react-intersection-observer: ^9.8.1
  react-lottie-player: ^1.5.4
  react-markdown: ^8.0.5
  react-qr-code: ^2.0.12
  react-refractor: ^3.1.1
  react-remove-scroll: ^2.5.10
  react-select: ^5.7.0
  react-textarea-autosize: ^8.5.3
  react-timeago: ^7.2.0
  react-tweet: ^3.1.1
  react-use-measure: ^2.1.1
  react-wrap-balancer: ^0.5.0
  refractor: ^4.8.1
  rehype-raw: ^6.1.1
  remark-gfm: ^3.0.1
  remeda: ^1.23.0
  rfc6902: ^5.0.1
  sanity: ^3.59.0
  sanity-plugin-markdown: ^5.0.0
  schema-dts: ^1.1.2
  slugify: ^1.6.6
  sonner: ^1.5.0
  storybook: ^8.0.0-beta.3
  styled-components: ^6.1.19
  supertest: ^6.3.3
  swagger-typescript-api: ^12.0.4
  tailwind-merge: ^2.3.0
  tailwind-scrollbar-hide: ^1.1.7
  tailwindcss: ^3.4.3
  tailwindcss-animate: ^1.0.7
  tailwindcss-safe-area: ^0.4.1
  tippy.js: ^6.3.7
  tsup: ^8.0.2
  typescript: ^5.4.3
  turbo: ^2.1.2
  use-debounce: ^9.0.4
  use-sound: ^4.0.1
  uuid: ^9.0.1
  vaul: ^0.9.1
  vitest: ^1.5.2
  wait-on: ^7.0.1
  yjs: ^13.6.8
  zod: ^3.22.3
  zustand: ^4.4.1
  github-markdown-css: ^5.8.1
  '@heroicons/react': ^2.2.0
  clsx: ^2.1.1
  copy-to-clipboard: ^3.3.3
  '@ant-design/icons': ^6.0.0
  '@mui/icons-material': ^7.1.0
  '@mui/material': ^7.1.0
  '@mui/x-tree-view': ^8.5.0
  '@emotion/styled': ^11.14.0
  prism-react-renderer: ^2.4.1
  material-file-icons: ^2.4.0
  colord: ^2.9.3
  merge-stream: ^2.0.0
  picomatch: ^4.0.3
