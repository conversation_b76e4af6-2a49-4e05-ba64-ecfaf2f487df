<%
const { apiConfig, routes, utils, config } = it;
const { info, servers, externalDocs } = apiConfig;
const { _, require, formatDescription } = utils;

const server = (servers && servers[0]) || { url: "" };

const descriptionLines = _.compact([
  `@title ${info.title || "No title"}`,
  info.version && `@version ${info.version}`,
  info.license && `@license ${_.compact([
    info.license.name,
    info.license.url && `(${info.license.url})`,
  ]).join(" ")}`,
  info.termsOfService && `@termsOfService ${info.termsOfService}`,
  server.url && `@baseUrl ${server.url}`,
  externalDocs.url && `@externalDocs ${externalDocs.url}`,
  info.contact && `@contact ${_.compact([
    info.contact.name,
    info.contact.email && `<${info.contact.email}>`,
    info.contact.url && `(${info.contact.url})`,
  ]).join(" ")}`,
  info.description && " ",
  info.description && _.replace(formatDescription(info.description), /\n/g, "\n * "),
]);

%>

declare const dataTagSymbol: unique symbol

export type DataTag<Type, Value> = Type & {
  [dataTagSymbol]: Value
}

function dataTaggedQueryKey<TData, TKey extends readonly unknown[] = unknown[]>(key: TKey): TKey & DataTag<TKey, TData>
function dataTaggedQueryKey(key: unknown) {
  return key
}

<% if (config.httpClientType === config.constants.HTTP_CLIENT.AXIOS) { %> import type { AxiosRequestConfig, AxiosResponse } from "axios"; <% } %>

<% if (descriptionLines.length) { %>
/**
<% descriptionLines.forEach((descriptionLine) => { %>
* <%~ descriptionLine %>

<% }) %>
*/
<% } %>
export class <%~ config.apiClassName %><SecurityDataType extends unknown><% if (!config.singleHttpClient) { %> extends HttpClient<SecurityDataType> <% } %> {

<% if(config.singleHttpClient) { %>
  http: HttpClient<SecurityDataType>;

  constructor (http: HttpClient<SecurityDataType>) {
    this.http = http;
  }
<% } %>


<% if (routes.outOfModule) { %>
  <% for (const route of routes.outOfModule) { %>

  <%~ includeFile('./keyed-procedure-call.ejs', { ...it, route }) %>

  <% } %>
<% } %>

<% if (routes.combined) { %>
  <% for (const { routes: combinedRoutes = [], moduleName } of routes.combined) { %>
  <%~ moduleName %> = {
  <% for (const route of combinedRoutes) { %>

  <%~ includeFile('./keyed-procedure-call.ejs', { ...it, route }) %>

  <% } %>
  }
  <% } %>
<% } %>
}
