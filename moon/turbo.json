{"$schema": "https://turbo.build/schema.json", "globalDependencies": [".env.*"], "globalEnv": ["NEXT_PUBLIC_CRATES_PRO_URL"], "tasks": {"dev": {"cache": false, "persistent": true}, "build": {"dependsOn": ["^build"], "outputs": ["dist/**", ".next/**"]}, "test": {"dependsOn": ["^build", "^test"], "outputs": []}, "start": {"dependsOn": ["build"], "cache": false, "persistent": true}, "lint": {"outputs": []}, "lint:fix": {"outputs": []}, "sentry:sourcemaps": {"outputs": []}, "clean": {"cache": false}}}